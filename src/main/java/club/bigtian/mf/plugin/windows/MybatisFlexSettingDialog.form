<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="club.bigtian.mf.plugin.windows.MybatisFlexSettingDialog">
  <grid id="cbd77" binding="contentPane" layout-manager="BorderLayout" hgap="0" vgap="0">
    <constraints>
      <xy x="36" y="54" width="1413" height="732"/>
    </constraints>
    <properties/>
    <border type="none"/>
    <children>
      <grid id="e3588" layout-manager="BorderLayout" hgap="0" vgap="0">
        <constraints border-constraint="North"/>
        <properties/>
        <border type="none"/>
        <children>
          <grid id="307fc" layout-manager="BorderLayout" hgap="10" vgap="0">
            <constraints border-constraint="Center"/>
            <properties/>
            <border type="none" title="常用配置"/>
            <children>
              <grid id="66a0a" layout-manager="GridLayoutManager" row-count="3" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                <margin top="0" left="0" bottom="0" right="0"/>
                <constraints border-constraint="South"/>
                <properties/>
                <border type="none"/>
                <children>
                  <tabbedpane id="959cb" binding="tabbedPane2" default-binding="true">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false">
                        <preferred-size width="200" height="200"/>
                      </grid>
                    </constraints>
                    <properties/>
                    <border type="none" title="基本配置"/>
                    <children>
                      <grid id="f2926" layout-manager="BorderLayout" hgap="0" vgap="0">
                        <constraints>
                          <tabbedpane title="包名匹配"/>
                        </constraints>
                        <properties/>
                        <border type="etched"/>
                        <children>
                          <grid id="45cc6" layout-manager="FlowLayout" hgap="5" vgap="5" flow-align="0">
                            <constraints border-constraint="Center"/>
                            <properties/>
                            <border type="none"/>
                            <children>
                              <component id="122e6" class="javax.swing.JLabel" binding="insideSchema">
                                <constraints/>
                                <properties>
                                  <text value="controller:"/>
                                </properties>
                              </component>
                              <component id="48a75" class="javax.swing.JTextField" binding="contrPath">
                                <constraints/>
                                <properties>
                                  <name value="contrPath"/>
                                  <preferredSize width="130" height="30"/>
                                  <text value="controller"/>
                                </properties>
                              </component>
                              <component id="ba7a8" class="javax.swing.JLabel">
                                <constraints/>
                                <properties>
                                  <text value="interface:"/>
                                </properties>
                              </component>
                              <component id="d76a1" class="javax.swing.JTextField" binding="servicePath">
                                <constraints/>
                                <properties>
                                  <name value="servicePath"/>
                                  <preferredSize width="130" height="30"/>
                                  <text value="service"/>
                                </properties>
                              </component>
                              <component id="f5c0a" class="javax.swing.JLabel">
                                <constraints/>
                                <properties>
                                  <text value="impl:"/>
                                </properties>
                              </component>
                              <component id="ea2ea" class="javax.swing.JTextField" binding="implPath">
                                <constraints/>
                                <properties>
                                  <name value="implPath"/>
                                  <preferredSize width="130" height="30"/>
                                  <text value="impl"/>
                                </properties>
                              </component>
                              <component id="ca00b" class="javax.swing.JLabel">
                                <constraints/>
                                <properties>
                                  <text value="model:"/>
                                </properties>
                              </component>
                              <component id="f5216" class="javax.swing.JTextField" binding="domainPath">
                                <constraints/>
                                <properties>
                                  <name value="domainPath"/>
                                  <preferredSize width="130" height="30"/>
                                  <text value="domain"/>
                                </properties>
                              </component>
                              <component id="c722f" class="javax.swing.JLabel">
                                <constraints/>
                                <properties>
                                  <text value="mapper:"/>
                                </properties>
                              </component>
                              <component id="ec85b" class="javax.swing.JTextField" binding="mapperPath">
                                <constraints/>
                                <properties>
                                  <name value="mapperPath"/>
                                  <preferredSize width="130" height="30"/>
                                  <text value="mapper"/>
                                </properties>
                              </component>
                              <component id="e5fcc" class="javax.swing.JLabel">
                                <constraints/>
                                <properties>
                                  <text value="xml"/>
                                </properties>
                              </component>
                              <component id="a5bba" class="javax.swing.JTextField" binding="xmlPath">
                                <constraints/>
                                <properties>
                                  <name value="xmlPath"/>
                                  <preferredSize width="130" height="30"/>
                                  <text value="mappers"/>
                                </properties>
                              </component>
                            </children>
                          </grid>
                        </children>
                      </grid>
                      <grid id="c5bbd" layout-manager="GridLayoutManager" row-count="2" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                        <margin top="0" left="0" bottom="0" right="0"/>
                        <constraints>
                          <tabbedpane title="前/后缀自定义"/>
                        </constraints>
                        <properties/>
                        <border type="etched"/>
                        <children>
                          <grid id="92ea4" layout-manager="FlowLayout" hgap="5" vgap="5" flow-align="0">
                            <constraints>
                              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties/>
                            <border type="none"/>
                            <children>
                              <component id="bcef1" class="javax.swing.JLabel">
                                <constraints/>
                                <properties>
                                  <text value="后缀："/>
                                </properties>
                              </component>
                              <component id="646a8" class="javax.swing.JLabel">
                                <constraints/>
                                <properties>
                                  <text value="controller:"/>
                                </properties>
                              </component>
                              <component id="24508" class="javax.swing.JTextField" binding="controllerSuffix">
                                <constraints/>
                                <properties>
                                  <name value="controllerSuffix"/>
                                  <preferredSize width="130" height="30"/>
                                  <text value="Controller"/>
                                </properties>
                              </component>
                              <component id="c257e" class="javax.swing.JLabel">
                                <constraints/>
                                <properties>
                                  <text value="interface:"/>
                                </properties>
                              </component>
                              <component id="b1f42" class="javax.swing.JTextField" binding="interfaceSuffix">
                                <constraints/>
                                <properties>
                                  <name value="interfaceSuffix"/>
                                  <preferredSize width="130" height="30"/>
                                  <text value="Service"/>
                                </properties>
                              </component>
                              <component id="31166" class="javax.swing.JLabel">
                                <constraints/>
                                <properties>
                                  <text value="impl:"/>
                                </properties>
                              </component>
                              <component id="d476b" class="javax.swing.JTextField" binding="implSuffix">
                                <constraints/>
                                <properties>
                                  <name value="implSuffix"/>
                                  <preferredSize width="130" height="30"/>
                                  <text value="ServiceImpl"/>
                                </properties>
                              </component>
                              <component id="c4f2b" class="javax.swing.JLabel">
                                <constraints/>
                                <properties>
                                  <text value="model:"/>
                                </properties>
                              </component>
                              <component id="f1bd" class="javax.swing.JTextField" binding="modelSuffix">
                                <constraints/>
                                <properties>
                                  <name value="modelSuffix"/>
                                  <preferredSize width="130" height="30"/>
                                  <text value="Entity"/>
                                </properties>
                              </component>
                              <component id="3368d" class="javax.swing.JLabel">
                                <constraints/>
                                <properties>
                                  <text value="mapper:"/>
                                </properties>
                              </component>
                              <component id="41bfe" class="javax.swing.JTextField" binding="mapperSuffix">
                                <constraints/>
                                <properties>
                                  <name value="mapperSuffix"/>
                                  <preferredSize width="130" height="30"/>
                                  <text value="Mapper"/>
                                </properties>
                              </component>
                            </children>
                          </grid>
                          <grid id="151f" layout-manager="GridLayoutManager" row-count="2" column-count="4" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                            <margin top="0" left="0" bottom="0" right="0"/>
                            <constraints>
                              <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties/>
                            <border type="none"/>
                            <children>
                              <component id="49603" class="javax.swing.JLabel">
                                <constraints>
                                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                                </constraints>
                                <properties>
                                  <text value="前缀："/>
                                </properties>
                              </component>
                              <hspacer id="d8c9f">
                                <constraints>
                                  <grid row="0" column="3" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                                </constraints>
                              </hspacer>
                              <vspacer id="672d4">
                                <constraints>
                                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                                </constraints>
                              </vspacer>
                              <component id="779cf" class="javax.swing.JLabel">
                                <constraints>
                                  <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="0" indent="0" use-parent-layout="false"/>
                                </constraints>
                                <properties>
                                  <text value="interface:"/>
                                </properties>
                              </component>
                              <component id="5669" class="javax.swing.JTextField" binding="interfacePre">
                                <constraints>
                                  <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="0" indent="0" use-parent-layout="false"/>
                                </constraints>
                                <properties>
                                  <name value="interfaceSuffix"/>
                                  <preferredSize width="130" height="30"/>
                                  <text value="I"/>
                                </properties>
                              </component>
                            </children>
                          </grid>
                        </children>
                      </grid>
                      <grid id="1fc0e" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                        <margin top="0" left="0" bottom="0" right="0"/>
                        <constraints>
                          <tabbedpane title="生成配置管理"/>
                        </constraints>
                        <properties>
                          <preferredSize width="954" height="30"/>
                        </properties>
                        <border type="none"/>
                        <children>
                          <grid id="40cfe" layout-manager="FlowLayout" hgap="5" vgap="5" flow-align="0">
                            <constraints>
                              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties/>
                            <border type="etched"/>
                            <children>
                              <component id="599a9" class="javax.swing.JCheckBox" binding="cacheCheckBox">
                                <constraints/>
                                <properties>
                                  <name value="cache"/>
                                  <text value="缓存方法"/>
                                </properties>
                              </component>
                              <component id="ac59e" class="javax.swing.JCheckBox" binding="overrideCheckBox">
                                <constraints/>
                                <properties>
                                  <name value="overrideCheckBox"/>
                                  <text value="是否覆盖"/>
                                </properties>
                              </component>
                              <component id="7801e" class="javax.swing.JCheckBox" binding="activeRecordCheckBox" default-binding="true">
                                <constraints/>
                                <properties>
                                  <text value="Active Record"/>
                                </properties>
                              </component>
                              <component id="7abea" class="javax.swing.JCheckBox" binding="ktFile">
                                <constraints/>
                                <properties>
                                  <hideActionText value="false"/>
                                  <text value="生成kt文件"/>
                                  <visible value="true"/>
                                </properties>
                              </component>
                              <component id="94243" class="javax.swing.JLabel">
                                <constraints/>
                                <properties>
                                  <text value="生成配置："/>
                                </properties>
                              </component>
                              <component id="91f96" class="javax.swing.JComboBox" binding="sinceConfigComBox">
                                <constraints/>
                                <properties>
                                  <lightWeightPopupEnabled value="true"/>
                                  <maximumRowCount value="10"/>
                                  <minimumSize width="200" height="30"/>
                                  <preferredSize width="166" height="30"/>
                                </properties>
                              </component>
                              <component id="bfec" class="javax.swing.JButton" binding="del">
                                <constraints/>
                                <properties>
                                  <icon value="icons/removeConfig.png"/>
                                  <selected value="false"/>
                                  <text value="删除"/>
                                </properties>
                              </component>
                              <component id="10feb" class="javax.swing.JButton" binding="clearAll">
                                <constraints/>
                                <properties>
                                  <icon value="icons/removeConfig.png"/>
                                  <text value="清空"/>
                                  <visible value="false"/>
                                </properties>
                              </component>
                              <component id="14345" class="com.intellij.openapi.ui.FixedSizeButton" binding="returnBtn">
                                <constraints/>
                                <properties>
                                  <actionCommand value=""/>
                                  <icon value="icons/return.png"/>
                                  <text value=""/>
                                  <toolTipText value="统一返回"/>
                                </properties>
                              </component>
                              <component id="463da" class="com.intellij.openapi.ui.FixedSizeButton" binding="buttonFixedSizeButton" default-binding="true">
                                <constraints/>
                                <properties>
                                  <horizontalTextPosition value="0"/>
                                  <icon value="icons/class.png"/>
                                  <text value=""/>
                                  <toolTipText value="实体类设置"/>
                                  <verticalAlignment value="0"/>
                                </properties>
                              </component>
                              <component id="3cba5" class="com.intellij.openapi.ui.FixedSizeButton" binding="variableBtn">
                                <constraints/>
                                <properties>
                                  <horizontalTextPosition value="0"/>
                                  <icon value="icons/variable.png"/>
                                  <text value=""/>
                                  <toolTipText value="自定义模板变量"/>
                                  <verticalAlignment value="0"/>
                                </properties>
                              </component>
                              <component id="44e33" class="javax.swing.JButton" binding="remoteBtn">
                                <constraints/>
                                <properties>
                                  <enabled value="true"/>
                                  <icon value="icons/remote.png"/>
                                  <text value="远程接口"/>
                                  <toolTipText value="设置远程接口"/>
                                </properties>
                              </component>
                            </children>
                          </grid>
                        </children>
                      </grid>
                      <grid id="863ee" layout-manager="GridLayoutManager" row-count="2" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                        <margin top="0" left="0" bottom="0" right="0"/>
                        <constraints>
                          <tabbedpane title="注解"/>
                        </constraints>
                        <properties/>
                        <border type="etched"/>
                        <children>
                          <grid id="c642b" layout-manager="FlowLayout" hgap="5" vgap="-1" flow-align="0">
                            <constraints>
                              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="1" fill="1" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties>
                              <enabled value="true"/>
                            </properties>
                            <border type="etched" title="Lombok"/>
                            <children>
                              <component id="a5685" class="javax.swing.JCheckBox" binding="builderCheckBox" default-binding="true">
                                <constraints/>
                                <properties>
                                  <name value="builder"/>
                                  <text value="@Builder"/>
                                </properties>
                              </component>
                              <component id="e0747" class="javax.swing.JCheckBox" binding="dataCheckBox" default-binding="true">
                                <constraints/>
                                <properties>
                                  <name value="data"/>
                                  <text value="@Data"/>
                                </properties>
                              </component>
                              <component id="e03b9" class="javax.swing.JCheckBox" binding="allArgsConstructorCheckBox" default-binding="true">
                                <constraints/>
                                <properties>
                                  <name value="allArgsConstructor"/>
                                  <text value="@AllArgsConstructor"/>
                                </properties>
                              </component>
                              <component id="af60b" class="javax.swing.JCheckBox" binding="noArgsConstructorCheckBox" default-binding="true">
                                <constraints/>
                                <properties>
                                  <name value="noArgsConstructor"/>
                                  <text value="@NoArgsConstructor"/>
                                </properties>
                              </component>
                              <component id="b2061" class="javax.swing.JCheckBox" binding="accessorsCheckBox">
                                <constraints/>
                                <properties>
                                  <text value="@Accessors"/>
                                </properties>
                              </component>
                              <component id="6efa7" class="javax.swing.JCheckBox" binding="requiredArgsConstructorCheckBox" default-binding="true">
                                <constraints/>
                                <properties>
                                  <text value="@RequiredArgsConstructor"/>
                                </properties>
                              </component>
                            </children>
                          </grid>
                          <grid id="18ee6" layout-manager="FlowLayout" hgap="5" vgap="-1" flow-align="0">
                            <constraints>
                              <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="1" fill="1" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties/>
                            <border type="etched" title="Swagger"/>
                            <children>
                              <component id="2ef51" class="javax.swing.JCheckBox" binding="swaggerCheckBox">
                                <constraints/>
                                <properties>
                                  <name value="swagger"/>
                                  <text value="Swagger2"/>
                                </properties>
                              </component>
                              <component id="8bea7" class="javax.swing.JCheckBox" binding="swagger3CheckBox" default-binding="true">
                                <constraints/>
                                <properties>
                                  <name value="swagger3"/>
                                  <text value="Swagger3"/>
                                </properties>
                              </component>
                            </children>
                          </grid>
                        </children>
                      </grid>
                      <grid id="1c9ff" layout-manager="GridLayoutManager" row-count="3" column-count="12" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                        <margin top="0" left="0" bottom="0" right="0"/>
                        <constraints>
                          <tabbedpane title=" 插件设置"/>
                        </constraints>
                        <properties/>
                        <border type="none"/>
                        <children>
                          <component id="76b78" class="javax.swing.JCheckBox" binding="fromCheckBox">
                            <constraints>
                              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties>
                              <selected value="true"/>
                              <text value="开启 From 检测"/>
                            </properties>
                          </component>
                          <vspacer id="d21b9">
                            <constraints>
                              <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                            </constraints>
                          </vspacer>
                          <component id="507e1" class="javax.swing.JLabel">
                            <constraints>
                              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties>
                              <text value="SQL 预览方言："/>
                            </properties>
                          </component>
                          <component id="8d6aa" class="javax.swing.JComboBox" binding="sqlDialect">
                            <constraints>
                              <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties/>
                          </component>
                          <component id="cbcd" class="javax.swing.JLabel">
                            <constraints>
                              <grid row="0" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties>
                              <text value="mapper xml 生成位置:"/>
                            </properties>
                          </component>
                          <component id="2818b" class="javax.swing.JComboBox" binding="mapperXmlType">
                            <constraints>
                              <grid row="0" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties>
                              <model>
                                <item value="resource"/>
                                <item value="java"/>
                              </model>
                            </properties>
                          </component>
                          <component id="c62d8" class="javax.swing.JCheckBox" binding="enableDebug">
                            <constraints>
                              <grid row="0" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties>
                              <text value="开启debug"/>
                              <toolTipText value="项目启动时，自动给项目的包路径设置debug模式"/>
                            </properties>
                          </component>
                          <component id="fca13" class="com.intellij.ui.components.OnOffButton" binding="databaseConfig">
                            <constraints>
                              <grid row="0" column="7" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties>
                              <offText value="所有"/>
                              <onText value="模块"/>
                              <text value="Button"/>
                            </properties>
                          </component>
                          <component id="f68bc" class="javax.swing.JLabel">
                            <constraints>
                              <grid row="0" column="6" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties>
                              <text value="数据库连接："/>
                            </properties>
                          </component>
                          <component id="5b15" class="javax.swing.JLabel">
                            <constraints>
                              <grid row="0" column="8" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties>
                              <text value="Preparing:"/>
                            </properties>
                          </component>
                          <component id="39da" class="javax.swing.JTextField" binding="preparing">
                            <constraints>
                              <grid row="0" column="9" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                                <preferred-size width="60" height="-1"/>
                              </grid>
                            </constraints>
                            <properties/>
                          </component>
                          <component id="e3fa8" class="javax.swing.JLabel">
                            <constraints>
                              <grid row="0" column="10" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties>
                              <text value="Parameters: "/>
                            </properties>
                          </component>
                          <component id="39dd8" class="javax.swing.JTextField" binding="parameters">
                            <constraints>
                              <grid row="0" column="11" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                                <preferred-size width="60" height="-1"/>
                              </grid>
                            </constraints>
                            <properties/>
                          </component>
                          <grid id="18345" layout-manager="GridLayoutManager" row-count="1" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                            <margin top="0" left="0" bottom="0" right="0"/>
                            <constraints>
                              <grid row="1" column="0" row-span="1" col-span="12" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                            </constraints>
                            <properties/>
                            <border type="none"/>
                            <children>
                              <component id="468b0" class="javax.swing.JCheckBox" binding="navigationMapper">
                                <constraints>
                                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                                </constraints>
                                <properties>
                                  <text value="Mapper和XMl跳转"/>
                                </properties>
                              </component>
                              <hspacer id="1156c">
                                <constraints>
                                  <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                                </constraints>
                              </hspacer>
                            </children>
                          </grid>
                        </children>
                      </grid>
                    </children>
                  </tabbedpane>
                  <grid id="d56bc" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                    <margin top="0" left="0" bottom="0" right="0"/>
                    <constraints>
                      <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <foreground color="-13947600"/>
                    </properties>
                    <border type="etched" title="自定义模板"/>
                    <children>
                      <grid id="bba40" layout-manager="GridLayoutManager" row-count="1" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                        <margin top="0" left="0" bottom="0" right="0"/>
                        <constraints>
                          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                        </constraints>
                        <properties/>
                        <border type="none"/>
                        <children>
                          <grid id="ef9e9" binding="listHeader" layout-manager="BorderLayout" hgap="0" vgap="0">
                            <constraints>
                              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false">
                                <preferred-size width="-1" height="40"/>
                              </grid>
                            </constraints>
                            <properties>
                              <enabled value="true"/>
                            </properties>
                            <border type="etched"/>
                            <children>
                              <component id="2a0ba" class="javax.swing.JList" binding="list1" default-binding="true">
                                <constraints border-constraint="Center"/>
                                <properties>
                                  <inheritsPopupMenu value="false"/>
                                  <layoutOrientation value="0"/>
                                  <model>
                                    <item value="AptTemplate.java.vm"/>
                                  </model>
                                  <preferredSize width="35" height="100"/>
                                  <selectedIndex value="0"/>
                                  <selectionMode value="0"/>
                                  <visibleRowCount value="5"/>
                                </properties>
                              </component>
                            </children>
                          </grid>
                          <grid id="c8cca" layout-manager="BorderLayout" hgap="0" vgap="0">
                            <constraints>
                              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false">
                                <preferred-size width="-1" height="430"/>
                              </grid>
                            </constraints>
                            <properties/>
                            <border type="none"/>
                            <children>
                              <grid id="3decb" binding="edtiorPanel" custom-create="true" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                                <margin top="0" left="0" bottom="0" right="0"/>
                                <constraints border-constraint="Center"/>
                                <properties>
                                  <preferredSize width="1080" height="280"/>
                                </properties>
                                <border type="none"/>
                                <children/>
                              </grid>
                            </children>
                          </grid>
                        </children>
                      </grid>
                    </children>
                  </grid>
                  <grid id="2ebc9" binding="testPanel" custom-create="true" layout-manager="BorderLayout" hgap="0" vgap="0">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties/>
                    <border type="none"/>
                    <children>
                      <scrollpane id="2f20a" class="com.intellij.ui.components.JBScrollPane">
                        <constraints border-constraint="Center"/>
                        <properties/>
                        <border type="none"/>
                        <children>
                          <component id="28eed" class="com.intellij.ui.components.JBScrollBar">
                            <constraints/>
                            <properties>
                              <orientation value="0"/>
                            </properties>
                          </component>
                        </children>
                      </scrollpane>
                    </children>
                  </grid>
                </children>
              </grid>
              <grid id="d72cb" layout-manager="GridLayoutManager" row-count="1" column-count="10" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                <margin top="0" left="0" bottom="0" right="0"/>
                <constraints border-constraint="North"/>
                <properties/>
                <border type="none"/>
                <children>
                  <component id="5c5ad" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="移除表前缀："/>
                    </properties>
                  </component>
                  <component id="cd011" class="com.intellij.ui.components.fields.ExpandableTextField" binding="tablePrefix">
                    <constraints>
                      <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                        <preferred-size width="115" height="-1"/>
                      </grid>
                    </constraints>
                    <properties>
                      <columns value="0"/>
                      <name value="tablePrefix"/>
                      <text value=""/>
                      <toolTipText value="多个以;分开"/>
                    </properties>
                  </component>
                  <component id="4e98a" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="作者："/>
                    </properties>
                  </component>
                  <component id="95f30" class="javax.swing.JTextField" binding="author">
                    <constraints>
                      <grid row="0" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                        <preferred-size width="115" height="-1"/>
                      </grid>
                    </constraints>
                    <properties>
                      <name value="author"/>
                      <text value=""/>
                    </properties>
                  </component>
                  <component id="f5fd7" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="0" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="版本："/>
                    </properties>
                  </component>
                  <component id="f98d9" class="javax.swing.JTextField" binding="since">
                    <constraints>
                      <grid row="0" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                        <preferred-size width="115" height="-1"/>
                      </grid>
                    </constraints>
                    <properties>
                      <name value="since"/>
                    </properties>
                  </component>
                  <component id="5f1d4" class="javax.swing.JButton" binding="exportBtn">
                    <constraints>
                      <grid row="0" column="6" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <icon value="icons/export.png"/>
                      <text value="导出"/>
                    </properties>
                  </component>
                  <component id="8ebf8" class="javax.swing.JButton" binding="importBtn">
                    <constraints>
                      <grid row="0" column="7" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <icon value="icons/import.png"/>
                      <text value="导入"/>
                    </properties>
                  </component>
                  <component id="fa15d" class="javax.swing.JButton" binding="resetBtn">
                    <constraints>
                      <grid row="0" column="8" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <icon value="icons/reset.png"/>
                      <text value="重置"/>
                    </properties>
                  </component>
                  <component id="a3c70" class="javax.swing.JButton" binding="saveBtn">
                    <constraints>
                      <grid row="0" column="9" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <icon value="icons/save.png"/>
                      <text value="保存"/>
                    </properties>
                  </component>
                </children>
              </grid>
            </children>
          </grid>
        </children>
      </grid>
    </children>
  </grid>
</form>
