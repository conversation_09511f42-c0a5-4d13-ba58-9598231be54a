<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="club.bigtian.mf.plugin.windows.TemplatePreviewDialog">
  <grid id="cbd77" binding="contentPane" layout-manager="GridLayoutManager" row-count="2" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
    <margin top="10" left="10" bottom="10" right="10"/>
    <constraints>
      <xy x="48" y="54" width="883" height="633"/>
    </constraints>
    <properties/>
    <border type="none"/>
    <children>
      <grid id="94766" layout-manager="GridLayoutManager" row-count="1" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
        <margin top="0" left="0" bottom="0" right="0"/>
        <constraints>
          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <hspacer id="98af6">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
            </constraints>
          </hspacer>
          <grid id="9538f" layout-manager="GridLayoutManager" row-count="1" column-count="2" same-size-horizontally="true" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <component id="e7465" class="javax.swing.JButton" binding="buttonOK">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="OK"/>
                </properties>
              </component>
              <component id="5723f" class="javax.swing.JButton" binding="buttonCancel">
                <constraints>
                  <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Cancel"/>
                </properties>
              </component>
            </children>
          </grid>
        </children>
      </grid>
      <grid id="e3588" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
        <margin top="0" left="0" bottom="0" right="0"/>
        <constraints>
          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <grid id="41646" layout-manager="GridLayoutManager" row-count="2" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <grid id="5a19" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                <margin top="0" left="0" bottom="0" right="0"/>
                <constraints>
                  <grid row="0" column="0" row-span="2" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties/>
                <border type="none"/>
                <children>
                  <component id="a345f" class="javax.swing.JList" binding="list1" default-binding="true">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="2" anchor="0" fill="3" indent="0" use-parent-layout="false">
                        <preferred-size width="50" height="50"/>
                      </grid>
                    </constraints>
                    <properties/>
                  </component>
                </children>
              </grid>
              <grid id="a93e4" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                <margin top="0" left="0" bottom="0" right="0"/>
                <constraints>
                  <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties/>
                <border type="none"/>
                <children>
                  <scrollpane id="a38be">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="7" hsize-policy="7" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties/>
                    <border type="none"/>
                    <children>
                      <component id="12a68" class="com.intellij.openapi.editor.impl.EditorComponentImpl" binding="textField1" custom-create="true" default-binding="true">
                        <constraints/>
                        <properties>
                          <autoscrolls value="true"/>
                          <editable value="true"/>
                        </properties>
                      </component>
                    </children>
                  </scrollpane>
                </children>
              </grid>
              <grid id="64e23" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                <margin top="0" left="0" bottom="0" right="0"/>
                <constraints>
                  <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties/>
                <border type="none"/>
                <children>
                  <scrollpane id="54ad8">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="7" hsize-policy="7" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties/>
                    <border type="none"/>
                    <children>
                      <component id="aa3aa" class="com.intellij.openapi.editor.impl.EditorComponentImpl" binding="textField2" custom-create="true" default-binding="true">
                        <constraints/>
                        <properties>
                          <editable value="true"/>
                        </properties>
                      </component>
                    </children>
                  </scrollpane>
                </children>
              </grid>
            </children>
          </grid>
        </children>
      </grid>
    </children>
  </grid>
</form>
