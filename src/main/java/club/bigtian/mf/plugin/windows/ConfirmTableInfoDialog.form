<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="club.bigtian.mf.plugin.windows.ConfirmTableInfoDialog">
  <grid id="cbd77" binding="contentPane" layout-manager="GridLayoutManager" row-count="2" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
    <margin top="10" left="10" bottom="10" right="10"/>
    <constraints>
      <xy x="48" y="54" width="436" height="297"/>
    </constraints>
    <properties/>
    <border type="none"/>
    <children>
      <grid id="94766" layout-manager="GridLayoutManager" row-count="1" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
        <margin top="0" left="0" bottom="0" right="0"/>
        <constraints>
          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <grid id="9538f" layout-manager="GridLayoutManager" row-count="1" column-count="2" same-size-horizontally="true" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <component id="e7465" class="javax.swing.JButton" binding="buttonOK">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="OK"/>
                </properties>
              </component>
              <component id="5723f" class="javax.swing.JButton" binding="buttonCancel">
                <constraints>
                  <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Cancel"/>
                </properties>
              </component>
            </children>
          </grid>
          <hspacer id="c3af2">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
            </constraints>
          </hspacer>
        </children>
      </grid>
      <grid id="e3588" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
        <margin top="0" left="0" bottom="0" right="0"/>
        <constraints>
          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <grid id="2f614" layout-manager="GridLayoutManager" row-count="2" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <grid id="22c28" layout-manager="FlowLayout" hgap="5" vgap="5" flow-align="0">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties/>
                <border type="none"/>
                <children>
                  <component id="8620e" class="javax.swing.JLabel">
                    <constraints/>
                    <properties>
                      <text value="Label"/>
                    </properties>
                  </component>
                  <component id="fbabf" class="javax.swing.JComboBox" binding="tableComBox">
                    <constraints/>
                    <properties>
                      <preferredSize width="250" height="30"/>
                    </properties>
                  </component>
                  <component id="aa73b" class="javax.swing.JCheckBox" binding="syncCheckBox">
                    <constraints/>
                    <properties>
                      <selected value="true"/>
                      <text value="同步修改"/>
                    </properties>
                  </component>
                </children>
              </grid>
              <scrollpane id="a35fc">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="7" hsize-policy="7" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties/>
                <border type="none"/>
                <children>
                  <component id="f5797" class="javax.swing.JTable" binding="dataTable">
                    <constraints/>
                    <properties>
                      <columnSelectionAllowed value="true"/>
                      <rowHeight value="30"/>
                    </properties>
                  </component>
                </children>
              </scrollpane>
            </children>
          </grid>
        </children>
      </grid>
    </children>
  </grid>
</form>
