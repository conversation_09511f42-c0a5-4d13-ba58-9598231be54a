package ${packageName};

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

class ${className} : TableDef("", "${talbeName}") {

#foreach($column in $list)
    val $column.name = QueryColumn(this, "${column.columnName}")
#end

    /**
     * 所有字段。
     */
    val $allColumns = QueryColumn(this, "*")

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    val $defaultColumns = arrayOf(#foreach($column in $list) #if($column.large==false)$column.name #if($foreach.hasNext),#end#end#end)


    companion object {
        val  $instance = ${className}()
    }
}
