package club.bigtian.mf.plugin.core.constant;

/**
 * 限定名称常数
 *
 * <AUTHOR>
 * @date 2024/04/28
 */
public class QualifiedNameConstant {

 public static final String SPRING_REQUEST_PARAM = "org.springframework.web.bind.annotation.RequestParam";
    public static final String SPRING_PATH_VARIABLE = "org.springframework.web.bind.annotation.PathVariable";
    public static final String SPRING_REQUEST_MAPPING = "org.springframework.web.bind.annotation.RequestMapping";
    public static final String SPRING_GET_MAPPING = "org.springframework.web.bind.annotation.GetMapping";
    public static final String SPRING_POST_MAPPING = "org.springframework.web.bind.annotation.PostMapping";
    public static final String SPRING_PUT_MAPPING = "org.springframework.web.bind.annotation.PutMapping";
    public static final String SPRING_DELETE_MAPPING = "org.springframework.web.bind.annotation.DeleteMapping";
    public static final String SPRING_PATCH_MAPPING = "org.springframework.web.bind.annotation.PatchMapping";
    public static final String SPRING_REQUEST_BODY = "org.springframework.web.bind.annotation.RequestBody";
    public static final String SPRING_RESPONSE_BODY = "org.springframework.web.bind.annotation.ResponseBody";
    public static final String SPRING_REST_CONTROLLER = "org.springframework.web.bind.annotation.RestController";
    public static final String SPRING_CONTROLLER = "org.springframework.stereotype.Controller";

    /**
     * MyBatis 相关注解
     */
    public static final String MYBATIS_PARAM = "org.apache.ibatis.annotations.Param";
    public static final String MYBATIS_MAPPER = "org.apache.ibatis.annotations.Mapper";
    public static final String MYBATIS_SELECT = "org.apache.ibatis.annotations.Select";
    public static final String MYBATIS_INSERT = "org.apache.ibatis.annotations.Insert";
    public static final String MYBATIS_UPDATE = "org.apache.ibatis.annotations.Update";
    public static final String MYBATIS_DELETE = "org.apache.ibatis.annotations.Delete";

    /**
     * Spring 相关注解
     */
    public static final String SPRING_SERVICE = "org.springframework.stereotype.Service";
    public static final String SPRING_COMPONENT = "org.springframework.stereotype.Component";
    public static final String SPRING_REPOSITORY = "org.springframework.stereotype.Repository";
    public static final String SPRING_AUTOWIRED = "org.springframework.beans.factory.annotation.Autowired";
    public static final String SPRING_QUALIFIER = "org.springframework.beans.factory.annotation.Qualifier";
    public static final String SPRING_VALUE = "org.springframework.beans.factory.annotation.Value";
}
