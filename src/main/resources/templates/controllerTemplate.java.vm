package $config.controllerPackage;

import com.mybatisflex.core.paginate.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
#if($config.interfacePackage.length() > 0)
import ${config.interfacePackage}.${interfaceName};
#end
#if($config.modelPackage.length() > 0)
import ${config.modelPackage}.${modelName};
#end
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.util.List;
#if($config.qualifiedName)
import ${config.qualifiedName};
#end
#if($config.swagger)
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiImplicitParam;
#end
#if($config.swagger3)
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
#end
/**
 * $!table.comment 控制层。
 *
 * <AUTHOR>
 * @since  $since
 */
@RestController
@RequestMapping("/$requestPath")
#if($config.swagger)
@Api(tags = "$!table.comment")
#end
#if($config.swagger3)
@Tag(name = "$!{table.comment}控制层")
#end
public class ${controllerName}{

    @Autowired
    private ${interfaceName} ${interfaceVariable};

/**
 * 添加 $!table.comment
 *
 * @param $requestPath $!table.comment
 * @return {@code true} 添加成功，{@code false} 添加失败
 */
    @PostMapping("/save")
    #if($config.swagger)
    @ApiOperation(value = "添加$!table.comment", notes = "添加$!table.comment")
    @ApiImplicitParams(value = {
        #foreach($column in $table.columnList)
                @ApiImplicitParam(name = "$column.fieldName", value = "$!column.comment" #if($column.notNull), required = $column.notNull #end) #if($foreach.hasNext),
        #end
        #end
    })
    #end
    #if($config.swagger3)
    @Operation(summary = "添加$!table.comment")
    @Parameters(value = {
        #foreach($column in $table.columnList)
                @Parameter(name = "$column.fieldName", description = "$!column.comment" #if($column.notNull), required = $column.notNull #end) #if($foreach.hasNext),
        #end
        #end
    })

    #end
#if($config.qualifiedName)
    public ${resultClass} #if($config.genericity)<Boolean> #end save(@RequestBody $modelName  $requestPath){
#if($config.resultType=='static')
        return ${resultClass}.${config.methodName}(${interfaceVariable}.save($requestPath));
#else
        return new ${resultClass}(${interfaceVariable}.save($requestPath));
#end
        }
#else
    public boolean save(@RequestBody $modelName  $requestPath){
        return ${interfaceVariable}.save($requestPath);
    }
#end


/**
 * 根据主键删除$!table.comment
 *
 * @param id 主键
 * @return {@code true} 删除成功，{@code false} 删除失败
 */
    @DeleteMapping("/remove/{id}")
    #if($config.swagger)
    @ApiOperation(value = "根据主键删除$!table.comment", notes = "根据主键删除$!table.comment")
    @ApiImplicitParams(value = {
        #foreach($column in $table.columnList)
            #if($column.primaryKey)
                    @ApiImplicitParam(name = "$column.fieldName", value = "$!column.comment", required = true)
            #end
        #end
    })
    #end
    #if($config.swagger3)
    @Operation(summary = "根据主键删除$!table.comment")
    @Parameters(value = {
        #foreach($column in $table.columnList)
            #if($column.primaryKey)
                    @Parameter(name = "$column.fieldName", description = "$!column.comment", required = true)
            #end
        #end
    })
    #end
#if($config.qualifiedName)
    public ${resultClass} #if($config.genericity)<Boolean> #end  remove(@PathVariable Serializable id){
    #if($config.resultType=='static')
            return ${resultClass}.${config.methodName}(${interfaceVariable}.removeById(id));
    #else
            return new ${resultClass}(${interfaceVariable}.removeById(id));
    #end
    }
#else
    public boolean remove(@PathVariable Serializable id){
        return ${interfaceVariable}.removeById(id);
    }
#end


/**
 * 根据主键更新$!table.comment
 *
 * @param $requestPath $!table.comment
 * @return {@code true} 更新成功，{@code false} 更新失败
 */
    @PutMapping("/update")
    #if($config.swagger)
    @ApiOperation(value = "根据主键更新$!table.comment", notes = "根据主键更新$!table.comment")
    @ApiImplicitParams(value = {
        #foreach($column in $table.columnList)
                @ApiImplicitParam(name = "$column.fieldName", value = "$!column.comment" #if($column.primaryKey), required = true #end) #if($foreach.hasNext),
        #end
        #end
    })
    #end
    #if($config.swagger3)
    @Operation(summary = "根据主键更新$!table.comment")
    @Parameters(value = {
        #foreach($column in $table.columnList)
                @Parameter(name = "$column.fieldName", description = "$!column.comment" #if($column.primaryKey), required = true #end) #if($foreach.hasNext),
        #end
        #end
    })
    #end
#if($config.qualifiedName)
    public  ${resultClass} #if($config.genericity)<Boolean> #end  update(@RequestBody $modelName $requestPath ){
    #if($config.resultType=='static')
            return ${resultClass}.${config.methodName}(${interfaceVariable}.updateById($requestPath));
    #else
            return new ${resultClass}(${interfaceVariable}.updateById($requestPath));
    #end
    }
#else
    public boolean update(@RequestBody $modelName $requestPath ){
        return ${interfaceVariable}.updateById($requestPath);
    }
#end


/**
 * 查询所有$!table.comment
 *
 * @return 所有数据
 */
    @GetMapping("/list")
    #if($config.swagger)
    @ApiOperation(value = "查询所有$!table.comment", notes = "查询所有$!table.comment")
    #end
    #if($config.swagger3)
    @Operation(summary = "查询所有$!table.comment")
    #end
#if($config.qualifiedName)
    public ${resultClass}#if($config.genericity)<List<$modelName>> #end list(){
    #if($config.resultType=='static')
            return ${resultClass}.${config.methodName}(${interfaceVariable}.list());
    #else
            return new ${resultClass}(${interfaceVariable}.list());
    #end
    }
#else
    public List<$modelName> list(){
        return ${interfaceVariable}.list();
    }
#end


/**
 * 根据$!{table.comment}主键获取详细信息。
 *
 * @param id ${requestPath}主键
 * @return $!{table.comment}详情
 */
    @GetMapping("/getInfo/{id}")
    #if($config.swagger)
    @ApiOperation(value = "根据$!{table.comment}主键获取详细信息", notes = "根据$!{table.comment}主键获取详细信息")
    @ApiImplicitParams(value = {
        #foreach($column in $table.columnList)
            #if($column.primaryKey)
                    @ApiImplicitParam(name = "$column.fieldName", value = "$!column.comment", required = true)
            #end
        #end
    })
    #end
    #if($config.swagger3)
    @Operation(summary = "根据$!{table.comment}主键获取详细信息")
    @Parameters(value = {
        #foreach($column in $table.columnList)
            #if($column.primaryKey)
                    @Parameter(name = "$column.fieldName", description = "$!column.comment", required = true)
            #end
        #end
    })
    #end
#if($config.qualifiedName)
    public ${resultClass}#if($config.genericity)<$modelName> #end  getInfo(@PathVariable Serializable id){
    #if($config.resultType=='static')
            return ${resultClass}.${config.methodName}(${interfaceVariable}.getById(id));
    #else
            return new ${resultClass}(${interfaceVariable}.getById(id));
    #end
    }
#else
    public $modelName getInfo(@PathVariable Serializable id){
        return ${interfaceVariable}.getById(id);
    }
#end


/**
 * 分页查询$!{table.comment}
 *
 * @param page 分页对象
 * @return 分页对象
 */
    @GetMapping("/page")
    #if($config.swagger)
    @ApiOperation(value = "分页查询$!{table.comment}", notes = "分页查询$!{table.comment}")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "pageNumber", value = "页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    #end
    #if($config.swagger3)
    @Operation(summary = "分页查询$!table.comment")
    @Parameters(value = {
            @Parameter(name = "pageNumber", description = "页码", required = true),
            @Parameter(name = "pageSize", description = "每页大小", required = true)
    })
    #end
#if($config.qualifiedName)
    public ${resultClass}#if($config.genericity)<Page<$modelName>> #end page(Page<$modelName> page){
    #if($config.resultType=='static')
            return ${resultClass}.${config.methodName}(${interfaceVariable}.page(page));
    #else
            return new ${resultClass}(${interfaceVariable}.page(page));
    #end
    }
#else
    public Page<$modelName> page(Page<$modelName> page){
        return ${interfaceVariable}.page(page);
    }
#end
}