<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${config.mapperPackage}.${mapperName}">
#if($config.modelPackage.length() > 0)
    <resultMap id="BaseResultMap" type="${config.modelPackage}.${modelName}">
#set($result = "")
#foreach($column in $table.columnList)
#set($result = $result + "`"+ ${column.name}+ "`")
#if($foreach.hasNext())
#set($result = $result + ", ")
#end
#if($column.primaryKey)
        <id column="${column.name}" jdbcType="$column.type" property="${column.fieldName}"/>
#else
        <result column="${column.name}" jdbcType="$column.type" property="${column.fieldName}"/>
#end
#end
    </resultMap>
#end
    <sql id="Base_Column_List">
        $result
    </sql>

</mapper>