<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin url="https://plugins.jetbrains.com/plugin/22158-mybatisflex-helpler" require-restart="true">
    <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
    <id>com.mybatisflex.bigtian</id>
    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
    <name>My<PERSON><PERSON>-<PERSON>lex-Helper</name>

    <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
    <vendor email="<EMAIL>" url="http://blog.bigtian.club/mybatisFlex_Helper/">时间淡忘一切</vendor>

    <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Simple HTML elements (text formatting, paragraphs, and lists) can be added inside of <![CDATA[ ]]> tag.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
    <description><![CDATA[
    <a href="https://blog.bigtian.club/">使用文档</a> |
    <a href="https://mybatis-flex.com/">MyBatis-Flex 官网</a> |
    <a href="https://www.bilibili.com/video/BV1WX4y1j7VQ/">介绍视频</a>
    <h3>Features:</h3>
    <ul>
    <li>The resulting APT file supports hints</li>
    <li>Code templates are highly customizable</li>
    <li>APT automatic compilation</li>
    <li>Share template-related configurations</li>
    <li>Automatically identify the packet path</li>
    <li>Preview SQL</li>
    <li>The apt field is renamed in batches</li>
    </ul>

    <ul>
    <li>APT文件支持代码提示</li>
    <li>代码模版高度自定义</li>
    <li>APT自动编译</li>
    <li>模板相关配置分享</li>
    <li>自动识别包路径</li>
    <li>预览SQL</li>
    <li>apt字段批量重命名</li>
    </ul>
    ]]></description>

    <change-notes><![CDATA[
    <h3>*******-RELEASE</h3>
    <ul>
    <li>【修复】column.notNull【表字段是否为空】返回值不对</li>
    <li>【优化】 过时api调整</li>
    <li>【优化】 支持K2</li>
    </ul>
    ]]></change-notes>
    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.modules.lang</depends>
    <depends>com.intellij.modules.java</depends>
    <depends>org.jetbrains.kotlin</depends>
    <depends>com.intellij.database</depends>
    <depends>com.intellij.spring.boot</depends>
    <depends>org.jetbrains.plugins.yaml</depends>
    <depends>com.intellij.spring</depends>

    <extensions defaultExtensionNs="org.jetbrains.kotlin">
        <supportsKotlinPluginMode supportsK2="true" />
    </extensions>
    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <java.programPatcher implementation="club.bigtian.mf.plugin.core.PerRun"/>

        <consoleFilterProvider implementation="club.bigtian.mf.plugin.core.console.MyBatisFlexConsoleFilterProvider" id="club.bigtian.mf.plugin.core.console.MyBatisFlexConsoleFilterProvider"/>
        <consoleFilterProvider implementation="club.bigtian.mf.plugin.core.console.MyBatisFlexAgentConsoleFilterProvider" id="club.bigtian.mf.plugin.core.console.MyBatisFlexAgentConsoleFilterProvider"/>
        <executor implementation="club.bigtian.mf.plugin.core.log.MyBatisLogExecutor"
                  id="club.bigtian.mf.plugin.core.log.MyBatisLogExecutor"/>

        <editorFactoryListener implementation="club.bigtian.mf.plugin.core.MybatisFlexDocumentChangeHandler" id="club.bigtian.mf.plugin.core.MybatisFlexDocumentChangeHandler"/>
        <completion.contributor language="kotlin"
                                id="club.bigtian.mf.plugin.core.contributor.MybatisFlexCompletionContributorKt"
                                implementationClass="club.bigtian.mf.plugin.core.contributor.MybatisFlexCompletionContributor"/>
        <completion.contributor language="JAVA"
                                id="club.bigtian.mf.plugin.core.contributor.MybatisFlexCompletionContributor"
                                implementationClass="club.bigtian.mf.plugin.core.contributor.MybatisFlexCompletionContributor"/>


        <completion.contributor language="any"
                                id="club.bigtian.mf.plugin.core.contributor.MybatisFlexConfigCompletionContributor"
                                implementationClass="club.bigtian.mf.plugin.core.contributor.MybatisFlexConfigCompletionContributor"/>

        <completion.contributor language="any"
                                id="club.bigtian.mf.plugin.core.contributor.MybatisFlexTemplateCompletionContributor"
                                implementationClass="club.bigtian.mf.plugin.core.contributor.MybatisFlexTemplateCompletionContributor"/>

        <!-- Service层APT补全贡献者 -->
        <completion.contributor language="JAVA"
                                id="club.bigtian.mf.plugin.core.contributor.ServiceCompletionContributor"
                                implementationClass="club.bigtian.mf.plugin.core.contributor.ServiceCompletionContributor"/>

        <!-- 项目启动监听器，用于预加载QueryColumn方法缓存 -->
        <projectActivity implementation="club.bigtian.mf.plugin.core.listener.ProjectStartupListener"/>

<!--        <completion.contributor language="JAVA"-->
<!--                                id="club.bigtian.mf.plugin.core.contributor.MybatisFlexTableDefCompletionContributor"-->
<!--                                implementationClass="club.bigtian.mf.plugin.core.contributor.MybatisFlexTableDefCompletionContributor"/>-->

        <completion.contributor language="JAVA"
                                id="club.bigtian.mf.plugin.core.contributor.ServiceCompletionContributor"
                                implementationClass="club.bigtian.mf.plugin.core.contributor.ServiceCompletionContributor"/>



        <fileIconProvider implementation="club.bigtian.mf.plugin.core.type.MybatisFlexConfigFileType" id="club.bigtian.mf.plugin.core.type.MybatisFlexConfigFileType"/>

        <annotator language="JAVA"
                   id="club.bigtian.mf.plugin.core.annotator.MybatisFlexConfigAnnotator"
                   order="first"
                   implementationClass="club.bigtian.mf.plugin.core.annotator.MybatisFlexConfigAnnotator"/>
        <annotator language="JAVA"
                   id="club.bigtian.mf.plugin.core.contributor.ServiceCompletionContributor"
                   order="first"
                   implementationClass="club.bigtian.mf.plugin.core.annotator.MybatisFlexConfigAnnotator"/>

        <annotator language="JAVA"
                   id="club.bigtian.mf.plugin.core.annotator.MybatisFlexMapperToXmlAnnotator"
                   order="first"
                   implementationClass="club.bigtian.mf.plugin.core.annotator.MybatisFlexMapperToXmlAnnotator"/>

        <annotator language="XML"
                   id="club.bigtian.mf.plugin.core.annotator.MybatisFlexXmlToMapperAnnotator"
                   order="first"
                   implementationClass="club.bigtian.mf.plugin.core.annotator.MybatisFlexXmlToMapperAnnotator"/>



        <!-- 语法校验，让没有生成代码的方法产生错误 -->
        <localInspection language="JAVA" shortName="NoFromInspection"
                         id="club.bigtian.mf.plugin.core.inspection.NoFromInspection"
                         displayName="From method inspection"

                         groupName="Mybatis felx" enabledByDefault="true" level="INFO"
                         implementationClass="club.bigtian.mf.plugin.core.inspection.NoFromInspection"/>
        <intentionAction>
            <className>club.bigtian.mf.plugin.core.internal.ParamInternal</className>
        </intentionAction>
        <intentionAction>
            <className>club.bigtian.mf.plugin.core.internal.AllParamInternal</className>
        </intentionAction>
        <intentionAction>
            <className>club.bigtian.mf.plugin.core.internal.SelectInternal</className>
        </intentionAction>
        <intentionAction>
            <className>club.bigtian.mf.plugin.core.internal.RequestParamInternal</className>
        </intentionAction>
        <intentionAction>
            <className>club.bigtian.mf.plugin.core.internal.AutoFillMethodParamsInternal</className>
        </intentionAction>
        <intentionAction>
            <className>club.bigtian.mf.plugin.core.internal.InsertInternal</className>
        </intentionAction>
        <intentionAction>
            <className>club.bigtian.mf.plugin.core.internal.DeleteInternal</className>
        </intentionAction>
        <intentionAction>
            <className>club.bigtian.mf.plugin.core.internal.UpdateInternal</className>
        </intentionAction>
        <intentionAction>
            <className>club.bigtian.mf.plugin.core.internal.ResultMapInternal</className>
        </intentionAction>

<!--        <toolWindow id="GPT" icon="/icons/GPT.png" anchor="right"-->
<!--                    factoryClass="club.bigtian.mf.plugin.core.factory.GptToolWindowFactory"/>-->
    </extensions>

    <actions>
        <action id="club.bigtian.mf.plugin.action.flex.TableAction" class="club.bigtian.mf.plugin.action.flex.TableAction"
                icon="/icons/flex.svg"
                text="Mybatis flex code generate" description="用于选择database中的table从而进行代码生成">
            <add-to-group group-id="DatabaseViewPopupMenu" anchor="first"/>
        </action>

        <action id="club.bigtian.mf.plugin.action.log.MyBatisLogAction"
                class="club.bigtian.mf.plugin.action.log.MyBatisLogAction"
                text="MyBatis Log Plugin" description="MyBatis log plugin free" icon="/icons/flex.svg">
            <add-to-group group-id="ToolsMenu" anchor="last"/>
            <add-to-group group-id="ConsoleEditorPopupMenu" anchor="before" relative-to-action="ConsoleView.ClearAll"/>
        </action>


        <action id="club.bigtian.mf.plugin.action.flex.AutoCompileAction" class="club.bigtian.mf.plugin.action.flex.AutoCompileAction" text="AutoCompile"
                description="AutoCompile" icon="/icons/flex.svg">
            <add-to-group group-id="GenerateGroup" anchor="first"/>
            <keyboard-shortcut keymap="$default" first-keystroke="meta W"/>
        </action>

        <action id="club.bigtian.mf.plugin.action.flex.SettingsAction" class="club.bigtian.mf.plugin.action.flex.SettingsAction" text="SettingsAction"
                description="AutoCompile" icon="/icons/flex.svg">
            <keyboard-shortcut keymap="$default" first-keystroke="meta s"/>
        </action>

        <action id="club.bigtian.mf.plugin.action.flex.SQLPreviewAction"
                class="club.bigtian.mf.plugin.action.flex.SQLPreviewAction" text="SQLPreview"
                description="SQLPreview" icon="/icons/flex.svg">
            <add-to-group group-id="GenerateGroup" anchor="first" />
        </action>


        <action id="SqlToCodeAction" class="club.bigtian.mf.plugin.action.flex.SqlToCodeAction" text="SQL转Flex代码" description="根据 sql 生成 flex 代码"  icon="/icons/flex.svg">
            <add-to-group group-id="GenerateGroup" anchor="first" />
        </action>

        <action id="NewDataBaseConnectAction" class="club.bigtian.mf.plugin.action.flex.NewDataBaseConnectAction" text="新建数据库连接" description="根据配置文件新建数据库连接"  icon="/icons/database.svg">
            <add-to-group group-id="GenerateGroup" anchor="first" />
            <keyboard-shortcut keymap="$default" first-keystroke="shift alt D"/>
        </action>
        <action id="RenameAptAction" class="club.bigtian.mf.plugin.action.flex.RenameAptAction" text="APT重命名" description="apt字段批量重命名"  icon="/icons/flex.svg">
            <add-to-group group-id="GenerateGroup" anchor="first" />
        </action>
    </actions>

    <idea-version since-build="231.*" until-build="253.*"/>
<!--    <resource-bundle/>-->
</idea-plugin>