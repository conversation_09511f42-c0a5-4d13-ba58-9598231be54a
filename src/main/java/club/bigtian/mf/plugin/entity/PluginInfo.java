package club.bigtian.mf.plugin.entity;


public  class PluginInfo {

    private int pluginid;
    private int downloads;
    private String channel;
    private String sinceuntil;
    private String until;
    private String since;
    private String notes;
    private String file;
    private String cdate;
    private String version;
    private String link;
    private int id;

    public int getPluginid() {
        return pluginid;
    }

    public void setPluginid(int pluginid) {
        this.pluginid = pluginid;
    }

    public int getDownloads() {
        return downloads;
    }

    public void setDownloads(int downloads) {
        this.downloads = downloads;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getSinceuntil() {
        return sinceuntil;
    }

    public void setSinceuntil(String sinceuntil) {
        this.sinceuntil = sinceuntil;
    }

    public String getUntil() {
        return until;
    }

    public void setUntil(String until) {
        this.until = until;
    }

    public String getSince() {
        return since;
    }

    public void setSince(String since) {
        this.since = since;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    public String getCdate() {
        return cdate;
    }

    public void setCdate(String cdate) {
        this.cdate = cdate;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}
