<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="club.bigtian.mf.plugin.windows.SettingsDialogWrapper">
  <grid id="27dc6" binding="rootPanel" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
    <margin top="0" left="0" bottom="0" right="0"/>
    <constraints>
      <xy x="20" y="20" width="500" height="400"/>
    </constraints>
    <properties/>
    <border type="none"/>
    <children>
      <tabbedpane id="bc07d">
        <constraints>
          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false">
            <preferred-size width="200" height="200"/>
          </grid>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <grid id="3410b" binding="filterTab" layout-manager="GridLayoutManager" row-count="3" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <tabbedpane title="Filter"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <component id="97458" class="javax.swing.JLabel">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Preparing:"/>
                </properties>
              </component>
              <component id="c0fc8" class="javax.swing.JTextField" binding="txtPreparing">
                <constraints>
                  <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                    <preferred-size width="150" height="-1"/>
                  </grid>
                </constraints>
                <properties>
                  <text value=""/>
                </properties>
              </component>
              <component id="a4a0e" class="javax.swing.JLabel">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Parameters:"/>
                </properties>
              </component>
              <component id="e3e35" class="javax.swing.JTextField" binding="txtParameters">
                <constraints>
                  <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                    <preferred-size width="150" height="-1"/>
                  </grid>
                </constraints>
                <properties/>
              </component>
              <grid id="fa3c5" layout-manager="GridLayoutManager" row-count="2" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                <margin top="0" left="0" bottom="0" right="0"/>
                <constraints>
                  <grid row="2" column="0" row-span="1" col-span="2" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties/>
                <border type="none"/>
                <children>
                  <component id="8dadf" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="Do not output sql log include the below keywords(add by row)"/>
                    </properties>
                  </component>
                  <component id="d3602" class="com.intellij.ui.components.JBTextArea" binding="txtKeywords">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="6" anchor="0" fill="3" indent="0" use-parent-layout="false">
                        <preferred-size width="150" height="50"/>
                      </grid>
                    </constraints>
                    <properties/>
                  </component>
                </children>
              </grid>
            </children>
          </grid>
          <grid id="674b4" binding="colorTab" layout-manager="GridLayoutManager" row-count="5" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <tabbedpane title="Color"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <component id="fb82a" class="javax.swing.JLabel">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Insert"/>
                </properties>
              </component>
              <component id="3f76d" class="javax.swing.JLabel">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Delete"/>
                </properties>
              </component>
              <component id="f494b" class="javax.swing.JLabel">
                <constraints>
                  <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Update"/>
                </properties>
              </component>
              <component id="a2dc9" class="javax.swing.JLabel">
                <constraints>
                  <grid row="3" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Select"/>
                </properties>
              </component>
              <vspacer id="ab0a7">
                <constraints>
                  <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                </constraints>
              </vspacer>
              <grid id="1947c" binding="insertColor" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                <margin top="0" left="0" bottom="0" right="0"/>
                <constraints>
                  <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="8" fill="2" indent="0" use-parent-layout="false">
                    <maximum-size width="50" height="-1"/>
                  </grid>
                </constraints>
                <properties>
                  <background color="-916992"/>
                </properties>
                <border type="none"/>
                <children/>
              </grid>
              <grid id="80d4a" binding="deleteColor" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                <margin top="0" left="0" bottom="0" right="0"/>
                <constraints>
                  <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="8" fill="2" indent="0" use-parent-layout="false">
                    <maximum-size width="50" height="-1"/>
                  </grid>
                </constraints>
                <properties>
                  <background color="-916992"/>
                </properties>
                <border type="none"/>
                <children/>
              </grid>
              <grid id="fc595" binding="updateColor" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                <margin top="0" left="0" bottom="0" right="0"/>
                <constraints>
                  <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="8" fill="2" indent="0" use-parent-layout="false">
                    <maximum-size width="50" height="-1"/>
                  </grid>
                </constraints>
                <properties>
                  <background color="-916992"/>
                </properties>
                <border type="none"/>
                <children/>
              </grid>
              <grid id="45a01" binding="selectColor" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                <margin top="0" left="0" bottom="0" right="0"/>
                <constraints>
                  <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="8" fill="2" indent="0" use-parent-layout="false">
                    <maximum-size width="50" height="-1"/>
                  </grid>
                </constraints>
                <properties>
                  <background color="-916992"/>
                </properties>
                <border type="none"/>
                <children/>
              </grid>
            </children>
          </grid>
        </children>
      </tabbedpane>
    </children>
  </grid>
</form>
