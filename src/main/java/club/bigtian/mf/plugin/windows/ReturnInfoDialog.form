<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="club.bigtian.mf.plugin.windows.ReturnInfoDialog">
  <grid id="cbd77" binding="contentPane" layout-manager="GridLayoutManager" row-count="2" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
    <margin top="10" left="10" bottom="10" right="10"/>
    <constraints>
      <xy x="48" y="54" width="435" height="228"/>
    </constraints>
    <properties/>
    <border type="none"/>
    <children>
      <grid id="94766" layout-manager="GridLayoutManager" row-count="1" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
        <margin top="0" left="0" bottom="0" right="0"/>
        <constraints>
          <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <hspacer id="98af6">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
            </constraints>
          </hspacer>
          <grid id="9538f" layout-manager="GridLayoutManager" row-count="1" column-count="2" same-size-horizontally="true" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <component id="e7465" class="javax.swing.JButton" binding="buttonOK">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="OK"/>
                </properties>
              </component>
              <component id="5723f" class="javax.swing.JButton" binding="buttonCancel">
                <constraints>
                  <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="Cancel"/>
                </properties>
              </component>
            </children>
          </grid>
        </children>
      </grid>
      <grid id="e3588" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
        <margin top="0" left="0" bottom="0" right="0"/>
        <constraints>
          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <grid id="b1ea9" layout-manager="GridLayoutManager" row-count="1" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties/>
            <border type="etched" title="统一返回配置"/>
            <children>
              <grid id="40fb5" layout-manager="FormLayout">
                <rowspec value="center:d:noGrow"/>
                <rowspec value="top:3dlu:noGrow"/>
                <rowspec value="center:max(d;4px):noGrow"/>
                <rowspec value="top:3dlu:noGrow"/>
                <rowspec value="center:max(d;4px):noGrow"/>
                <rowspec value="top:3dlu:noGrow"/>
                <rowspec value="center:max(d;4px):noGrow"/>
                <colspec value="fill:d:noGrow"/>
                <colspec value="left:4dlu:noGrow"/>
                <colspec value="fill:d:grow"/>
                <colspec value="left:4dlu:noGrow"/>
                <colspec value="fill:d:noGrow"/>
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties/>
                <border type="none"/>
                <children>
                  <component id="c56cb" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <forms/>
                    </constraints>
                    <properties>
                      <text value="类："/>
                    </properties>
                  </component>
                  <component id="343ba" class="javax.swing.JTextField" binding="classField">
                    <constraints>
                      <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                        <preferred-size width="150" height="-1"/>
                      </grid>
                      <forms defaultalign-horz="false"/>
                    </constraints>
                    <properties>
                      <editable value="false"/>
                      <enabled value="true"/>
                    </properties>
                  </component>
                  <component id="a2aab" class="com.intellij.openapi.ui.FixedSizeButton" binding="buttonFixedSizeButton" default-binding="true">
                    <constraints>
                      <grid row="0" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                      <forms/>
                    </constraints>
                    <properties>
                      <text value="Button"/>
                    </properties>
                  </component>
                  <component id="1b8f6" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <forms/>
                    </constraints>
                    <properties>
                      <text value="方法："/>
                    </properties>
                  </component>
                  <component id="ed248" class="javax.swing.JComboBox" binding="methodComBox">
                    <constraints>
                      <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                      <forms/>
                    </constraints>
                    <properties>
                      <toolTipText value="只支持单个参数的方法"/>
                    </properties>
                  </component>
                  <component id="6c799" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="4" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <forms/>
                    </constraints>
                    <properties>
                      <text value="方式："/>
                    </properties>
                  </component>
                  <grid id="3b12b" layout-manager="FlowLayout" hgap="5" vgap="5" flow-align="0">
                    <constraints>
                      <grid row="4" column="2" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
                      <forms/>
                    </constraints>
                    <properties/>
                    <border type="none"/>
                    <children>
                      <component id="b22b" class="javax.swing.JRadioButton" binding="staticRadio">
                        <constraints/>
                        <properties>
                          <selected value="true"/>
                          <text value="Static"/>
                        </properties>
                      </component>
                      <component id="b5ddf" class="javax.swing.JRadioButton" binding="newRadio">
                        <constraints/>
                        <properties>
                          <text value="New"/>
                        </properties>
                      </component>
                    </children>
                  </grid>
                  <component id="2a736" class="javax.swing.JCheckBox" binding="genericityCheckBox">
                    <constraints>
                      <grid row="6" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <forms/>
                    </constraints>
                    <properties>
                      <selected value="true"/>
                      <text value=""/>
                    </properties>
                  </component>
                  <component id="11fb0" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="6" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                      <forms/>
                    </constraints>
                    <properties>
                      <text value="泛型"/>
                    </properties>
                  </component>
                </children>
              </grid>
            </children>
          </grid>
        </children>
      </grid>
    </children>
  </grid>
</form>
