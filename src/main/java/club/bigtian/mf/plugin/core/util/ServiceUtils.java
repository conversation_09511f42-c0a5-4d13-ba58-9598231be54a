package club.bigtian.mf.plugin.core.util;

import com.intellij.psi.PsiClass;
import com.intellij.psi.PsiClassType;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiType;
import com.intellij.psi.util.PsiTreeUtil;

/**
 * Service层工具类
 * Author: BigTian
 */
public class ServiceUtils {

    private static final String ISERVICE_CLASS = "com.baomidou.mybatisplus.extension.service.IService";
    private static final String SERVICE_IMPL_CLASS = "com.baomidou.mybatisplus.extension.service.impl.ServiceImpl";

    /**
     * 检查当前编辑的文件是否是Service接口或实现类
     *
     * @param psiElement 当前位置的PsiElement
     * @return 是否是Service相关类
     */
    public static boolean isServiceRelated(PsiElement psiElement) {
        PsiClass psiClass = PsiTreeUtil.getParentOfType(psiElement, PsiClass.class);
        if (psiClass == null) {
            return false;
        }

        return isServiceInterface(psiClass) || isServiceImpl(psiClass);
    }

    /**
     * 检查类是否是Service接口
     *
     * @param psiClass 类
     * @return 是否是Service接口
     */
    public static boolean isServiceInterface(PsiClass psiClass) {
        if (!psiClass.isInterface()) {
            return false;
        }

        // 检查是否继承自IService
        for (PsiClassType extendType : psiClass.getExtendsListTypes()) {
            String canonicalText = extendType.getCanonicalText();
            if (canonicalText.startsWith(ISERVICE_CLASS + "<")) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查类是否是ServiceImpl实现类
     *
     * @param psiClass 类
     * @return 是否是ServiceImpl
     */
    public static boolean isServiceImpl(PsiClass psiClass) {
        if (psiClass.isInterface() || psiClass.isEnum() || psiClass.isAnnotationType()) {
            return false;
        }

        // 检查是否继承自ServiceImpl
        for (PsiClassType extendType : psiClass.getExtendsListTypes()) {
            String canonicalText = extendType.getCanonicalText();
            if (canonicalText.startsWith(SERVICE_IMPL_CLASS + "<")) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从Service接口中提取实体类名
     *
     * @param psiClass Service接口
     * @return 实体类名
     */
    public static String extractEntityFromInterface(PsiClass psiClass) {
        if (!psiClass.isInterface()) {
            return null;
        }

        // 检查接口是否继承自IService
        for (PsiClassType extendType : psiClass.getExtendsListTypes()) {
            String canonicalText = extendType.getCanonicalText();
            if (canonicalText.startsWith(ISERVICE_CLASS + "<")) {
                PsiType[] parameters = extendType.getParameters();
                if (parameters.length > 0) {
                    return parameters[0].getPresentableText();
                }
            }
        }

        return null;
    }

    /**
     * 从ServiceImpl实现类中提取实体类名
     *
     * @param psiClass ServiceImpl实现类
     * @return 实体类名
     */
    public static String extractEntityFromImpl(PsiClass psiClass) {
        if (psiClass.isInterface() || psiClass.isEnum() || psiClass.isAnnotationType()) {
            return null;
        }

        // 检查是否继承自ServiceImpl
        for (PsiClassType extendType : psiClass.getExtendsListTypes()) {
            String canonicalText = extendType.getCanonicalText();
            if (canonicalText.startsWith(SERVICE_IMPL_CLASS + "<")) {
                PsiType[] parameters = extendType.getParameters();
                if (parameters.length >= 2) {
                    return parameters[1].getPresentableText();
                }
            }
        }

        return null;
    }

    /**
     * 从ServiceImpl实现类中提取Mapper类名
     *
     * @param psiClass ServiceImpl实现类
     * @return Mapper类名
     */
    public static String extractMapperFromImpl(PsiClass psiClass) {
        if (psiClass.isInterface() || psiClass.isEnum() || psiClass.isAnnotationType()) {
            return null;
        }

        // 检查是否继承自ServiceImpl
        for (PsiClassType extendType : psiClass.getExtendsListTypes()) {
            String canonicalText = extendType.getCanonicalText();
            if (canonicalText.startsWith(SERVICE_IMPL_CLASS + "<")) {
                PsiType[] parameters = extendType.getParameters();
                if (parameters.length >= 2) {
                    return parameters[0].getPresentableText();
                }
            }
        }

        return null;
    }
}