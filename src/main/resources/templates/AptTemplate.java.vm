package $packageName;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

public class ${className} extends TableDef{

    public static final ${className} $instance=new ${className}();

    #foreach($column in $list)
        public final QueryColumn $column.name =new QueryColumn(this,"${column.columnName}");
    #end
    /**
     * 所有字段。
     */
    public final QueryColumn $allColumns=new QueryColumn(this,"*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[]$defaultColumns=new QueryColumn[]{#foreach($column in $list) #if($column.large==false)$column.name #if($foreach.hasNext),#end#end#end};
    public ${className}(){super("","${talbeName}");}
    private ${className}(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }
    public ${className} as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new ${className}("", "${talbeName}", alias));
    }
}
