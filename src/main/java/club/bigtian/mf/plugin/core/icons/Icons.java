package club.bigtian.mf.plugin.core.icons;

import com.intellij.openapi.util.IconLoader;

import javax.swing.*;

/**
 * Icons
 *
 * https://jetbrains.design/intellij/resources/icons_list/
 * 
 * <AUTHOR>
 */
public class Icons {
    public static final Icon MY_BATIS = IconLoader.getIcon("/icons/ibatis.svg", Icons.class);
    public static final Icon FLEX = IconLoader.getIcon("/icons/flex.svg", Icons.class);
    public static final Icon DONATE = IconLoader.getIcon("/icons/coffee.svg", Icons.class);
    public static final Icon PRETTY_PRINT = IconLoader.getIcon("/icons/prettyPrint.svg", Icons.class);
    public static final Icon SQL_PREVIEW = IconLoader.getIcon("/icons/sqlPreview.svg", Icons.class);
    public static final Icon MOVE_UP = IconLoader.getIcon("/icons/moveUp.svg", Icons.class);
    public static final Icon MOVE_DOWN = IconLoader.getIcon("/icons/moveDown.svg", Icons.class);
    public static final Icon EYE = IconLoader.getIcon("/icons/eye.svg", Icons.class);
    public static final Icon FLEX_XML = IconLoader.getIcon("/icons/flex_xml.svg", Icons.class);
    public static final Icon FLEX_MAPPER = IconLoader.getIcon("/icons/flex_mapper.svg", Icons.class);
}