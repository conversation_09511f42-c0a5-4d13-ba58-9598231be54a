package $config.modelPackage;

#if($config.data||$config.activeRecord)
import lombok.Data;
#end
#if($config.allArgsConstructor)
import lombok.AllArgsConstructor;
#end
#if($config.noArgsConstructor)
import lombok.NoArgsConstructor;
#end
#if($config.requiredArgsConstructor)
import lombok.RequiredArgsConstructor;
#end
#if($config.builder)
import lombok.Builder;
#end
#if($config.accessors)
import lombok.experimental.Accessors;
#end
#if($config.swagger)
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
#end
#if($config.swagger3)
import io.swagger.v3.oas.annotations.media.Schema;
#end
#if($config.activeRecord)
import com.mybatisflex.core.activerecord.Model;
#end
import com.mybatisflex.annotation.Column;
#foreach($column in $table.columnList)
#if($column.primaryKey)
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
#end
#end
import com.mybatisflex.annotation.Table;
#foreach($classImport in $importClassList)
import $classImport;
#end

/**
 * $!table.comment 实体类。
 *
 * <AUTHOR>
 * @since $since
 */
#if($config.accessors)
@Accessors(chain = true)
#end
#if($config.requiredArgsConstructor)
@RequiredArgsConstructor
#end
#if($config.data||$config.activeRecord)
@Data#if($config.activeRecord)(staticConstructor = "create") #end
#end
#if($config.builder)
@Builder
#end
#if($config.noArgsConstructor)
@NoArgsConstructor
#end
#if($config.allArgsConstructor)
@AllArgsConstructor
#end
#if($config.swagger)
@ApiModel(value = "$!table.comment", description = "$!table.comment")
#end
#if($config.swagger3)
@Schema(name = "$table.comment")
#end
@Table(value = "$table.name"#if($config.dataSource) , dataSource = "${config.dataSource}" #end#if($table.onInsert) , onInsert = ${table.onInsert}.class #end#if($table.onUpdate) , onUpdate = ${table.onUpdate}.class #end#if($table.onSet) , onSet = ${table.onSet}.class #end)
public class ${modelName} #if($config.activeRecord) extends Model<${modelName}>  #end #if(${config.modelSuperClass}) extends ${table.superClass}  #end {

    #foreach($column in $table.columnList)
    #if($column.comment.length()>0)
    /**
    * $!column.comment
    */
    #end
    #if($config.swagger && $column.comment)
    @ApiModelProperty(value = "$column.comment")
    #end
    #if($config.swagger3 && $column.comment)
    @Schema(description = "$column.comment")
    #end
    #if($column.primaryKey)
    @Id(keyType = KeyType.$config.idType)
    #else
    @Column(value = "$column.name" #if($column.logicDelete), isLogicDelete = true #end #if($column.tenant), tenantId = true #end #if($column.version), version = true #end #if($column.insertValue), onInsertValue = "$column.insertValue" #end#if($column.updateValue), onUpdateValue = "$column.updateValue" #end)
    #end
    private $column.fieldType  $column.fieldName;

#end

#if($config.data==false&& $config.activeRecord==false)
    #foreach($column in $table.columnList)
    public  $column.fieldType  get${column.methodName}() {
        return ${column.fieldName};
    }

    public void set${column.methodName}(${column.fieldType} ${column.fieldName}) {
        this.${column.fieldName} = ${column.fieldName};
    }
#end
#end
        }
