<?xml version="1.0" encoding="UTF-8"?>
<form xmlns="http://www.intellij.com/uidesigner/form/" version="1" bind-to-class="club.bigtian.mf.plugin.windows.MybatisFlexCodeGenerateDialog">
  <grid id="cbd77" binding="contentPane" layout-manager="GridLayoutManager" row-count="2" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
    <margin top="10" left="10" bottom="10" right="10"/>
    <constraints>
      <xy x="48" y="54" width="1055" height="404"/>
    </constraints>
    <properties>
      <preferredSize width="616" height="200"/>
    </properties>
    <border type="none"/>
    <children>
      <grid id="94766" layout-manager="GridLayoutManager" row-count="1" column-count="3" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
        <margin top="0" left="0" bottom="0" right="0"/>
        <constraints>
          <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="1" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <grid id="9538f" layout-manager="GridLayoutManager" row-count="1" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <component id="e7465" class="javax.swing.JButton" binding="generateBtn">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <enabled value="true"/>
                  <requestFocusEnabled value="true"/>
                  <text value="生成"/>
                  <visible value="true"/>
                </properties>
              </component>
              <component id="5723f" class="javax.swing.JButton" binding="cancelBtn">
                <constraints>
                  <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="取消"/>
                </properties>
              </component>
            </children>
          </grid>
          <hspacer id="f41cc">
            <constraints>
              <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="1" hsize-policy="6" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
            </constraints>
          </hspacer>
          <component id="983e1" class="javax.swing.JButton" binding="button1" default-binding="true">
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties>
              <icon value="icons/mapping.png"/>
              <text value="映射"/>
            </properties>
          </component>
        </children>
      </grid>
      <grid id="e3588" layout-manager="GridLayoutManager" row-count="2" column-count="2" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
        <margin top="0" left="0" bottom="0" right="0"/>
        <constraints>
          <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <grid id="4e84c" layout-manager="GridLayoutManager" row-count="1" column-count="5" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="2" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <component id="e833" class="javax.swing.JLabel">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <foreground color="-8420987"/>
                  <text value="ID生成策略："/>
                </properties>
              </component>
              <component id="3c08a" class="javax.swing.JComboBox" binding="idTypeCombox">
                <constraints>
                  <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <model>
                    <item value="Auto"/>
                    <item value="None"/>
                    <item value="Sequence"/>
                    <item value="Generator"/>
                  </model>
                </properties>
              </component>
              <component id="b7074" class="com.intellij.openapi.ui.FixedSizeButton" binding="settingLabel">
                <constraints>
                  <grid row="0" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <icon value="icons/setting.png"/>
                  <text value=""/>
                  <toolTipText value="设置"/>
                </properties>
              </component>
              <component id="ac4c8" class="javax.swing.JLabel">
                <constraints>
                  <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="生成配置："/>
                </properties>
              </component>
              <component id="2ccff" class="javax.swing.JComboBox" binding="sinceComBox">
                <constraints>
                  <grid row="0" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <model>
                    <item value="添加配置"/>
                  </model>
                  <selectedIndex value="-1"/>
                </properties>
              </component>
            </children>
          </grid>
          <vspacer id="bcf69">
            <constraints>
              <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
            </constraints>
          </vspacer>
          <grid id="32199" layout-manager="GridLayoutManager" row-count="2" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <vspacer id="907fa">
                <constraints>
                  <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="6" hsize-policy="1" anchor="0" fill="2" indent="0" use-parent-layout="false"/>
                </constraints>
              </vspacer>
              <grid id="984e2" binding="modelPanel" layout-manager="GridLayoutManager" row-count="6" column-count="7" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
                <margin top="0" left="0" bottom="0" right="0"/>
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="true"/>
                </constraints>
                <properties/>
                <border type="etched" title="生成配置"/>
                <children>
                  <component id="ec8ed" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="model："/>
                    </properties>
                  </component>
                  <component id="4b6a0" class="com.intellij.ui.components.fields.ExpandableTextField" binding="modelPackagePath">
                    <constraints>
                      <grid row="1" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                        <preferred-size width="150" height="-1"/>
                      </grid>
                    </constraints>
                    <properties>
                      <inheritsPopupMenu value="false"/>
                    </properties>
                  </component>
                  <component id="bdf27" class="com.intellij.openapi.ui.FixedSizeButton" binding="modelBtn">
                    <constraints>
                      <grid row="1" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value=""/>
                    </properties>
                  </component>
                  <component id="d69f8" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="2" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="service inteface："/>
                    </properties>
                  </component>
                  <component id="938d4" class="com.intellij.ui.components.fields.ExpandableTextField" binding="serviceIntefacePath">
                    <constraints>
                      <grid row="2" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                        <preferred-size width="150" height="-1"/>
                      </grid>
                    </constraints>
                    <properties>
                      <columns value="10"/>
                    </properties>
                  </component>
                  <component id="69e80" class="com.intellij.openapi.ui.FixedSizeButton" binding="serviceInterfaceBtn">
                    <constraints>
                      <grid row="2" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="Button"/>
                    </properties>
                  </component>
                  <component id="62767" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="controller："/>
                    </properties>
                  </component>
                  <component id="20590" class="com.intellij.ui.components.fields.ExpandableTextField" binding="controllerPath">
                    <constraints>
                      <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                        <preferred-size width="150" height="-1"/>
                      </grid>
                    </constraints>
                    <properties/>
                  </component>
                  <component id="d6184" class="com.intellij.openapi.ui.FixedSizeButton" binding="controllerBtn">
                    <constraints>
                      <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="Button"/>
                    </properties>
                  </component>
                  <component id="6e755" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="4" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="mapper："/>
                    </properties>
                  </component>
                  <component id="a2654" class="com.intellij.ui.components.fields.ExpandableTextField" binding="mapperPackagePath">
                    <constraints>
                      <grid row="4" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                        <preferred-size width="150" height="-1"/>
                      </grid>
                    </constraints>
                    <properties>
                      <columns value="10"/>
                    </properties>
                  </component>
                  <component id="c01a" class="com.intellij.openapi.ui.FixedSizeButton" binding="mapperBtn">
                    <constraints>
                      <grid row="4" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value=""/>
                    </properties>
                  </component>
                  <component id="316a3" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="0" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="所属模块："/>
                    </properties>
                  </component>
                  <component id="53dab" class="javax.swing.JComboBox" binding="cotrollerCombox">
                    <constraints>
                      <grid row="0" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <editable value="true"/>
                      <model/>
                    </properties>
                  </component>
                  <component id="be6e3" class="javax.swing.JComboBox" binding="modelCombox">
                    <constraints>
                      <grid row="1" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <editable value="true"/>
                    </properties>
                  </component>
                  <component id="f89fa" class="javax.swing.JComboBox" binding="mapperComBox">
                    <constraints>
                      <grid row="4" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <editable value="true"/>
                    </properties>
                  </component>
                  <component id="f19fd" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="1" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="所属模块："/>
                    </properties>
                  </component>
                  <component id="8285e" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="4" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="所属模块："/>
                    </properties>
                  </component>
                  <component id="1387a" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="3" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="service impl："/>
                    </properties>
                  </component>
                  <component id="c12f9" class="com.intellij.openapi.ui.FixedSizeButton" binding="serviceImplBtn">
                    <constraints>
                      <grid row="3" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="Button"/>
                    </properties>
                  </component>
                  <component id="1955c" class="com.intellij.ui.components.fields.ExpandableTextField" binding="serviceImpPath">
                    <constraints>
                      <grid row="3" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                        <preferred-size width="150" height="-1"/>
                      </grid>
                    </constraints>
                    <properties/>
                  </component>
                  <component id="90ce9" class="javax.swing.JComboBox" binding="serviceInteCombox">
                    <constraints>
                      <grid row="2" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <editable value="true"/>
                    </properties>
                  </component>
                  <component id="a3450" class="javax.swing.JComboBox" binding="serviceImplComBox">
                    <constraints>
                      <grid row="3" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <editable value="true"/>
                    </properties>
                  </component>
                  <component id="494de" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="2" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="所属模块："/>
                    </properties>
                  </component>
                  <component id="f2ddb" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="3" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="所属模块："/>
                    </properties>
                  </component>
                  <component id="676c8" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="5" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="mapper xml"/>
                    </properties>
                  </component>
                  <component id="e6b86" class="com.intellij.ui.components.fields.ExpandableTextField" binding="mapperXmlPath">
                    <constraints>
                      <grid row="5" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                        <preferred-size width="150" height="-1"/>
                      </grid>
                    </constraints>
                    <properties/>
                  </component>
                  <component id="bebd" class="com.intellij.openapi.ui.FixedSizeButton" binding="mapperXmlBtn">
                    <constraints>
                      <grid row="5" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value=""/>
                    </properties>
                  </component>
                  <component id="2047e" class="javax.swing.JLabel">
                    <constraints>
                      <grid row="5" column="4" row-span="1" col-span="1" vsize-policy="0" hsize-policy="0" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="所属模块："/>
                    </properties>
                  </component>
                  <component id="d56a1" class="javax.swing.JComboBox" binding="xmlComBox">
                    <constraints>
                      <grid row="5" column="5" row-span="1" col-span="1" vsize-policy="0" hsize-policy="2" anchor="8" fill="1" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <editable value="true"/>
                      <enabled value="true"/>
                    </properties>
                  </component>
                  <component id="24e37" class="javax.swing.JCheckBox" binding="syncCheckBox">
                    <constraints>
                      <grid row="0" column="6" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <text value="同步"/>
                    </properties>
                  </component>
                  <component id="5551a" class="javax.swing.JCheckBox" binding="enableControllerBox">
                    <constraints>
                      <grid row="0" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <hideActionText value="false"/>
                      <name value="controllerPackage"/>
                      <selected value="true"/>
                      <text value=""/>
                      <toolTipText value="是否生成"/>
                      <verifyInputWhenFocusTarget value="false"/>
                    </properties>
                  </component>
                  <component id="379c2" class="javax.swing.JCheckBox" binding="enableModelBox">
                    <constraints>
                      <grid row="1" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <name value="modelPackage"/>
                      <selected value="true"/>
                      <text value=""/>
                      <toolTipText value="是否生成"/>
                    </properties>
                  </component>
                  <component id="82c9d" class="javax.swing.JCheckBox" binding="enableInterBox">
                    <constraints>
                      <grid row="2" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <name value="interfacePackage"/>
                      <selected value="true"/>
                      <text value=""/>
                      <toolTipText value="是否生成"/>
                    </properties>
                  </component>
                  <component id="7e441" class="javax.swing.JCheckBox" binding="enableImplBox">
                    <constraints>
                      <grid row="3" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <name value="implPackage"/>
                      <selected value="true"/>
                      <text value=""/>
                      <toolTipText value="是否生成"/>
                    </properties>
                  </component>
                  <component id="4281a" class="javax.swing.JCheckBox" binding="enableMapperBox">
                    <constraints>
                      <grid row="4" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <name value="mapperPackage"/>
                      <selected value="true"/>
                      <text value=""/>
                      <toolTipText value="是否生成"/>
                    </properties>
                  </component>
                  <component id="c1584" class="javax.swing.JCheckBox" binding="enableXmlBox">
                    <constraints>
                      <grid row="5" column="3" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                    </constraints>
                    <properties>
                      <name value="xmlPackage"/>
                      <selected value="true"/>
                      <text value=""/>
                      <toolTipText value="是否生成"/>
                    </properties>
                  </component>
                </children>
              </grid>
            </children>
          </grid>
        </children>
      </grid>
      <grid id="d3bef" layout-manager="GridLayoutManager" row-count="2" column-count="1" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
        <margin top="0" left="0" bottom="0" right="0"/>
        <constraints>
          <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
        </constraints>
        <properties/>
        <border type="none"/>
        <children>
          <grid id="b4c2a" layout-manager="GridLayoutManager" row-count="1" column-count="3" same-size-horizontally="false" same-size-vertically="false" hgap="-1" vgap="-1">
            <margin top="0" left="0" bottom="0" right="0"/>
            <constraints>
              <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="3" hsize-policy="3" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <component id="253b6" class="javax.swing.JCheckBox" binding="selectAllChexBox">
                <constraints>
                  <grid row="0" column="0" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="8" fill="0" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <text value="全选"/>
                </properties>
              </component>
              <component id="81efe" class="javax.swing.JTextField" binding="tableSearch">
                <constraints>
                  <grid row="0" column="1" row-span="1" col-span="1" vsize-policy="0" hsize-policy="6" anchor="8" fill="1" indent="0" use-parent-layout="false">
                    <preferred-size width="150" height="-1"/>
                  </grid>
                </constraints>
                <properties/>
              </component>
              <component id="6c94a" class="com.intellij.openapi.ui.FixedSizeButton" binding="sortBtn">
                <constraints>
                  <grid row="0" column="2" row-span="1" col-span="1" vsize-policy="0" hsize-policy="3" anchor="0" fill="1" indent="0" use-parent-layout="false"/>
                </constraints>
                <properties>
                  <icon value="icons/sort.png"/>
                  <text value=""/>
                  <toolTipText value="升序"/>
                </properties>
              </component>
            </children>
          </grid>
          <scrollpane id="33575">
            <constraints>
              <grid row="1" column="0" row-span="1" col-span="1" vsize-policy="7" hsize-policy="7" anchor="0" fill="3" indent="0" use-parent-layout="false"/>
            </constraints>
            <properties/>
            <border type="none"/>
            <children>
              <component id="2272e" class="javax.swing.JList" binding="tableList">
                <constraints/>
                <properties>
                  <model/>
                </properties>
              </component>
            </children>
          </scrollpane>
        </children>
      </grid>
    </children>
  </grid>
</form>
