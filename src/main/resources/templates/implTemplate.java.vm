
package $config.implPackage;


import org.springframework.stereotype.Service;
#if($config.interfacePackage.length() > 0)
import ${config.interfacePackage}.${interfaceName};
#end
#if($config.modelPackage.length() > 0)
import ${config.modelPackage}.${modelName};
#end
#if($config.mapperPackage.length() > 0)
import $config.mapperPackage.${mapperName};
#end
#if($config.cache)
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
#end
import com.mybatisflex.spring.service.impl.ServiceImpl;
/**
 * $!table.comment 服务层实现。
 *
 * <AUTHOR>
 * @since $since
 */
@Service
public class ${implName} extends ServiceImpl<${mapperName},${modelName}> implements ${interfaceName} {

#if($config.cache)
	@Override
	@CacheEvict(allEntries = true)
	public boolean remove(QueryWrapper query) {
		return super.remove(query);
	}

	@Override
	@CacheEvict(key = "#id")
	public boolean removeById(Serializable id) {
		return super.removeById(id);
	}

	@Override
	@CacheEvict(allEntries = true)
	public boolean removeByIds(Collection<? extends Serializable> ids) {
		return super.removeByIds(ids);
	}

	@Override
	@CacheEvict(allEntries = true)
	public boolean update($modelName entity, QueryWrapper query) {
		return super.update(entity, query);
	}

	@Override
	@CacheEvict(key = "#entity.#(primaryKey)")
	public boolean updateById($modelName entity, boolean ignoreNulls) {
		return super.updateById(entity, ignoreNulls);
	}

	@Override
	@CacheEvict(allEntries = true)
	public boolean updateBatch(Collection<$modelName> entities, int batchSize) {
		return super.updateBatch(entities, batchSize);
	}

	@Override
	@Cacheable(key = "#id")
	public $modelName getById(Serializable id) {
		return super.getById(id);
	}

	@Override
	@Cacheable(key = "#root.methodName + ':' + #query.toSQL()")
	public $modelName getOne(QueryWrapper query) {
		return super.getOne(query);
	}

	@Override
	@Cacheable(key = "#root.methodName + ':' + #query.toSQL()")
	public <R> R getOneAs(QueryWrapper query, Class<R> asType) {
		return super.getOneAs(query, asType);
	}

	@Override
	@Cacheable(key = "#root.methodName + ':' + #query.toSQL()")
	public List<$modelName> list(QueryWrapper query) {
		return super.list(query);
	}

	@Override
	@Cacheable(key = "#root.methodName + ':' + #query.toSQL()")
	public <R> List<R> listAs(QueryWrapper query, Class<R> asType) {
		return super.listAs(query, asType);
	}

	/**
	 * @deprecated 无法通过注解进行缓存操作。
	 */
	@Override
	@Deprecated
	public List<$modelName> listByIds(Collection<? extends Serializable> ids) {
		return super.listByIds(ids);
	}

	@Override
	@Cacheable(key = "#root.methodName + ':' + #query.toSQL()")
	public long count(QueryWrapper query) {
		return super.count(query);
	}

	@Override
	@Cacheable(key = "#root.methodName + ':' + #page.getPageSize() + ':' + #page.getPageNumber() + ':' + #query.toSQL()")
	public <R> Page<R> pageAs(Page<R> page, QueryWrapper query, Class<R> asType) {
		return super.pageAs(page, query, asType);
	}
#end
}