package club.bigtian.mf.plugin.core.constant;

public class MybatisFlexConstant {

    /**
     * 控制器模板
     */
    public static final String CONTROLLER_TEMPLATE = "controllerTemplate.java";
    public static final String TABLE_PREFIX = "tablePrefix";
    /**
     * 逻辑删除
     */
    public static final String LOGIC_DELETE_FIELD = "logicDeleteField";
    public static final String MODEL_TEMPLATE = "modelTemplate.java";
    public static final String INTERFACE_TEMPLATE = "interfaceTempalate.java";
    public static final String IMPL_TEMPLATE = "implTemplate.java";
    public static final String MAPPER_TEMPLATE = "mapperTemplate.java";
    public static final String XML_TEMPLATE = "xmlTemplate.xml";
    public static final String LOMBOK_BUILDER = "builder";
    public static final String LOMBOK_ACCESSORS = "accessors";
    public static final String LOMBOK_DATA = "data";
    public static final String LOMBOK_ALL_ARGS_CONSTRUCTOR = "allArgsConstructor";
    public static final String LOMBOK_NO_ARGS_CONSTRUCTOR = "noArgsConstructor";
    public static final String SWAGGER = "swagger";
    public static final String AUTHOR = "author";
    public static final String SINCE = "since";

    public static final String MYBATIS_FLEX_CONFIG = "mybatisFlexConfig";
    public static final String MYBATIS_FLEX_CONFIGSINCE = "configSince";
    public static final String XML_COM_BOX = "xmlComBox";
    public static final String MAPPER_COM_BOX = "mapperComBox";
    public static final String SERVICE_IMPL_COM_BOX = "serviceImplComBox";
    public static final String SERVICE_INTE_COMBOX = "serviceInteCombox";
    public static final String MODEL_COMBOX = "modelCombox";
    public static final String COTROLLER_COMBOX = "cotrollerCombox";
    public static final String SYNC_CHECK_BOX = "syncCheckBox";
    public static final String CONTROLLER_PATH = "controllerPath";
    public static final String MAPPER_XML_PATH = "mapperXmlPath";
    public static final String MAPPER_PACKAGE_PATH = "mapperPackagePath";
    public static final String SERVICE_IMP_PATH = "serviceImpPath";
    public static final String SERVICE_INTEFACE_PATH = "serviceIntefacePath";
    public static final String MODEL_PACKAGE_PATH = "modelPackagePath";
    public static final String CACHE = "cache";
    public static final String OVERRIDE = "overrideCheckBox";
    public static final String NOTICE_GROUP_ID = "mybatis-flex";
    public static final String SWAGGER3 = "swagger3";
    public static final String CONTROLLER_SUFFIX = "controllerSuffix";
    public static final String INTERFACE_SUFFIX = "interfaceSuffix";
    public static final String INTERFACE_PRE = "interfacePre";
    public static final String IMPL_SUFFIX = "implSuffix";
    public static final String MODEL_SUFFIX = "modelSuffix";
    public static final String MAPPER_SUFFIX = "mapperSuffix";
    public static final String MAPPERS = "mappers";
    public static final String TENANT = "tenant";
    public static final String VERSION = "version";
    public static final String STRING = "static";
    public static final String NEW = "new";
    public static final String CONTROLLER = "Controller";
    public static final String ENTITY = "Entity";
    public static final String DOMAIN = "domain";
    public static final String SERVICE = "Service";
    public static final String SERVICE_IMPL = "ServiceImpl";
    public static final String IMPL = "impl";
    public static final String MAPPER = "Mapper";

    public static final String CONTR_PATH = "contrPath";
    public static final String SERVICE_PATH = "servicePath";
    public static final String IMPL_PATH = "implPath";
    public static final String DOMAIN_PATH = "domainPath";
    public static final String XML_PATH = "xmlPath";
    public static final String MAPPER_PATH = "mapperPath";
    public static final String MYBATISFLEX_CORE_BASE_MAPPER = "com.mybatisflex.core.BaseMapper";
    public static final String ANNOTATION_AUTOWIRED = "org.springframework.beans.factory.annotation.Autowired";
    public static final String ANNOTATION_RESOURCE = "javax.annotation.Resource";
    public static final String ACTIVE_RECORD = "activeRecord";

    public static final String LOMBOK_REQUIRED_ARGS_CONSTRUCTOR = "requiredArgsConstructor";
    public static final String DATA_SOURCE = "dataSource";
    public static final String MODEL_SUPER_CLASS = "modelSuperClass";
    public static final String FROM = "fromCheck";
    public static final String KT_FILE = "ktFile";
    public static final String SQL_DIALECT = "sqlDialect";
    public static final String TAB_LIST = "tabList";
    public static final String ENABLE_DEBUG = "enableDebug";
    public static final String ENABLE_LOG = "enableLog";
    public static final String XML = "Xml";
    public static final String DATABASE_CONFIG = "databaseConfig";
    public static final String PREPARING = "preparing";
    public static final String PARAMETERS = "parameters";
    public static final String NAVIGATION_MAPPER = "navigationMapper";
    /**
     * mybatisCodeHelperProMarketPlace
     */
    public static final String MYBATIS_PLUGIN_ID = "com.ccnode.codegenerator.MyBatisCodeHelperProMarketPlace";
    /**
     * MyBatisCodeHelperPro
     */
    public static final String MY_BATIS_CODE_HELPER_PRO_ID = "com.ccnode.codegenerator.MyBatisCodeHelperPro";

    public static String MAPPER_XML_TYPE= "mapperXmlType";
}
